# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Building

**Build AgentService (default release build):**
```bash
swift build -c release --package-path AgentService
```

**Build with static llama.cpp (for deployment without runtime dependencies):**
```bash
chmod +x scripts/build-llama-static.sh
LLAMA_CPP_REF=b6550 ./scripts/build-llama-static.sh
```

**Build prebuilt xcframework (for SwiftPM binary target consumption):**
```bash
chmod +x scripts/build-prebuilt-xcframework.sh
LLAMA_CPP_REF=b6550 ./scripts/build-prebuilt-xcframework.sh
```

### Running

**Start AgentService with default configuration:**
```bash
swift run --package-path AgentService AgentService
```

**Start with custom configuration:**
```bash
AGENT_CONFIG=agent-config.json swift run --package-path AgentService AgentService
```

### Testing

**Run tests:**
```bash
swift test --package-path AgentService
```

## Architecture

### Project Structure

This is a Swift-based local LLM runtime for macOS/iOS that integrates with the FountainKit ecosystem. The codebase is organized as:

- **AgentService**: Swift package providing HTTP service hosting local LLMs
  - Exposes OpenAI-compatible `/chat` and `/chat/stream` endpoints
  - Supports multiple backends: llama.cpp (via custom C wrapper), CoreML, and mock
  - Built on SwiftNIO for async networking

- **FountainKitIntegration**: Configuration and persona definitions for FountainKit gateway integration

### Key Components

**Backend System**: The service uses a pluggable backend architecture (`ModelBackend` protocol) with implementations for:
- `LlamaCppBackend`: Wraps llama.cpp C API for GGUF model inference
- `CoreMLBackend`: Uses Apple's Core ML for hardware-accelerated inference
- `MockBackend`: Testing/development backend

**HTTP Layer**: SwiftNIO-based HTTP server (`NIOHTTPServer`) with router handling:
- `POST /chat`: Synchronous chat completion endpoint
- `POST /chat/stream`: Server-sent events streaming endpoint
- `GET /health`: Health check endpoint

**Configuration**: Loaded from `agent-config.json` or `AGENT_CONFIG` environment variable:
- `backend`: Model backend to use (llama/coreml/mock)
- `modelPath`: Path to GGUF or Core ML model file
- `host`/`port`: Service binding configuration

### llama.cpp Integration

The project owns its llama.cpp wrapper rather than depending on third-party packages:

1. **LlamaCppC System Library**: Exposes llama.cpp C API to Swift via module.modulemap
2. **Static Build Option**: `build-llama-static.sh` compiles llama.cpp and statically links it
3. **XCFramework Build**: Creates SwiftPM-consumable binary target for distribution

The wrapper approach gives full control over the inference stack and avoids external dependencies.

## Development Workflow

When modifying the llama.cpp integration:
1. Update C wrapper code in `AgentService/Sources/LlamaCppC/`
2. Rebuild using appropriate script (static or xcframework)
3. Test with different GGUF models from Hugging Face

When adding new endpoints or features:
1. Extend `Router.swift` with new route handling
2. Implement corresponding logic in backend implementations
3. Update `HTTPModels.swift` with any new request/response types

## Model Support

Recommended function-calling models (GGUF format):
- `gorilla-llm/gorilla-openfunctions-v2-gguf`
- `NousResearch/Hermes-2-Pro-Mistral-7B-GGUF`
- `fireworks-ai/firefunction-v1` (via Ollama)

Models should be placed in a `Models/` directory and referenced in `agent-config.json`.

## CI/CD

GitHub Actions workflow (`release.yml`) builds and releases:
- Plain AgentService binary (requires llama.cpp at runtime)
- Static-linked AgentService (includes llama.cpp, pinned to b6550 commit)
- LlamaCppCBinary.xcframework (for SwiftPM distribution)

All builds target macOS arm64 (Apple Silicon). The `LLAMA_CPP_REF` environment variable controls which llama.cpp version is used.

## Troubleshooting

### HTTP Server Issues

**Server exits on first external request:**
- **Symptom**: `curl http://127.0.0.1:8080/health` returns "Empty reply from server" and the process exits
- **Cause**: NIO HTTP handler concurrency issue in `NIOHTTPServer.swift`
- **Status**: Known issue, patch applied but may need additional fixes
- **Workaround**: Unit tests pass (in-process kernel works); external HTTP requests currently fail

**Testing endpoints without external HTTP:**
```bash
# Run unit tests to verify kernel logic
swift test --package-path AgentService

# Check specific test cases
swift test --package-path AgentService --filter testHealthEndpoint
swift test --package-path AgentService --filter testFunctionCallAuto
```

**Debugging HTTP layer:**
```bash
# Build with debug symbols
swift build --package-path AgentService

# Run with verbose output (when fixed)
swift run --package-path AgentService AgentService
```

### Backend Configuration

**LlamaCpp backend not available:**
- Ensure `brew install llama.cpp` or build from source
- Verify `canImport(LlamaCppC)` resolves correctly
- Falls back to mock backend if unavailable

**CoreML backend not available:**
- Only available on Apple platforms with CoreML framework
- Falls back to mock backend if unavailable

## Current Status

- ✅ Build system: Working (SwiftPM + SwiftNIO)
- ✅ Unit tests: All passing (5/5)
- ✅ Mock backend: Functional for development
- ⚠️  HTTP server: External requests cause process exit
- ⚠️  LlamaCpp backend: Conditional compilation, requires system library
- ⚠️  CoreML backend: Scaffold only, needs implementation

## Quick Validation

**Verify build and tests:**
```bash
swift build --package-path AgentService
swift test --package-path AgentService
```

**Expected test output:**
```
Test Suite 'AgentCoreTests' passed at [timestamp]
Executed 5 tests, with 0 failures (0 unexpected) in 0.005 seconds
```

**API contracts (validated by tests):**
- GET /health → 200 "ok"
- POST /chat → OpenAI-compatible JSON response
- POST /chat/stream → SSE with text/event-stream