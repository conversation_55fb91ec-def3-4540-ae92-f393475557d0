# Examples

This directory contains example applications demonstrating how to use the SwiftAgent SDK.

## Running the Example App

> [!IMPORTANT]
> You must open the `SwiftAgent.xcworkspace` file at the root of the repository, **not** the `ExampleApp.xcodeproj` file in the `Example App/` folder.

### Steps to run:

1. Open `SwiftAgent.xcworkspace` (located at the root of the repository)
2. Select the "ExampleApp" scheme in Xcode
3. Choose your target device or simulator
4. Build and run

The workspace is configured to use the local development version of the SDK, allowing you to test changes immediately without needing to publish or update package dependencies.
