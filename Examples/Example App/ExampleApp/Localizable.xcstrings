{"sourceLanguage": "en", "strings": {"Agent": {"comment": "A section header that indicates the section related to the agent.", "isCommentAutoGenerated": true}, "Ask Agent": {"comment": "A button label that triggers the action of asking the agent a question.", "isCommentAutoGenerated": true}, "Ask me anything…": {"comment": "A placeholder text for a text field where the user can input their question.", "isCommentAutoGenerated": true}, "Error": {"comment": "A label for an error message.", "isCommentAutoGenerated": true}, "Response": {"comment": "A label displayed above the section that shows the agent's response.", "isCommentAutoGenerated": true}, "SwiftAgent": {"comment": "The title of the main view in the example.", "isCommentAutoGenerated": true}, "Thinking…": {"comment": "A placeholder text that appears while the agent is processing the user's input.", "isCommentAutoGenerated": true}, "Tools Used": {"comment": "A label for the section that lists the tools used by the agent.", "isCommentAutoGenerated": true}}, "version": "1.1"}