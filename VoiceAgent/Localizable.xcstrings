{"sourceLanguage": "en", "strings": {"agent.listening": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Agent is listening, start talking"}}}}, "connect.connecting": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Connecting"}}}}, "connect.simulator": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Running on Simulator, camera and screen sharing will be unavailable."}}}}, "connect.start": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Start call"}}}}, "connect.tip": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Need help getting set up? Check out the [Voice AI quickstart](https://docs.livekit.io/agents/start/voice-ai/)."}}}}, "error.ok": {"localizations": {"en": {"stringUnit": {"state": "translated", "value": "OK"}}}}, "error.title": {"localizations": {"en": {"stringUnit": {"state": "translated", "value": "Error"}}}}, "message.placeholder": {"localizations": {"en": {"stringUnit": {"state": "translated", "value": "Message"}}}}, "warning.reconnecting": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Trying to reconnect"}}}}, "warning.title": {"extractionState": "manual", "localizations": {"en": {"stringUnit": {"state": "translated", "value": "Warning"}}}}}, "version": "1.0"}