{"originHash": "c9e7f5e38eab39ff3e8c9d5112d7e58687014e0877b5697cead504e72e11444b", "pins": [{"identity": "metacodable", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyLab/MetaCodable.git", "state": {"revision": "e2b09b4ceed09c972adb7f48572446d07a1c0e16", "version": "1.5.0"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "309a47b2b1d9b5e991f36961c983ecec72275be3", "version": "1.6.1"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "8c0c0a8b49e080e54e5e328cc552821ff07cd341", "version": "1.2.1"}}, {"identity": "swift-openai-responses", "kind": "remoteSourceControl", "location": "https://github.com/SwiftedMind/swift-openai-responses", "state": {"branch": "main", "revision": "fd6c3f6a0909266bb50e6d16cec3a9fbf79245d7"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax.git", "state": {"revision": "f99ae8aa18f0cf0d53481901f88a0991dc3bd4a2", "version": "601.0.1"}}], "version": 3}